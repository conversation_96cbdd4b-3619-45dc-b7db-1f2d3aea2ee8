2025-06-04 08:33:12,722 - ma_service - DEBUG - rule_numbers值:
[54, 58, 46, 81, 61]
2025-06-04 08:33:12,741 - ma_service - DEBUG - num值:54
2025-06-04 08:33:12,744 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=54
2025-06-04 08:33:12,747 - ma_service - DEBUG - new_dict数据结构:
{'id': 964, 'CPU': 'Intel X86'}
2025-06-04 08:33:12,748 - ma_service - DEBUG - num值:58
2025-06-04 08:33:12,751 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=58
2025-06-04 08:33:12,753 - ma_service - DEBUG - new_dict数据结构:
{'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10'}
2025-06-04 08:33:12,754 - ma_service - DEBUG - num值:46
2025-06-04 08:33:12,757 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=46
2025-06-04 08:33:12,759 - ma_service - DEBUG - new_dict数据结构:
{'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4'}
2025-06-04 08:33:12,760 - ma_service - DEBUG - num值:81
2025-06-04 08:33:12,763 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=81
2025-06-04 08:33:12,766 - ma_service - DEBUG - new_dict数据结构:
{'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的'}
2025-06-04 08:33:12,767 - ma_service - DEBUG - num值:61
2025-06-04 08:33:12,770 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=61
2025-06-04 08:33:12,772 - ma_service - DEBUG - new_dict数据结构:
{'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}
2025-06-04 08:33:12,774 - ma_service - DEBUG - new_list数据结构:
[{'id': 855, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 856, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 857, '用途': '用途 非生产', '操作系统': 'RHEL 8.3', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 858, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 办公', '数据库': 'OceanBase 4'}, {'id': 859, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 860, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 861, '用途': '用途 非生产', '操作系统': 'RHEL 8.10', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 903, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 904, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 905, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 906, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 907, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 908, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 909, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 910, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 911, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 912, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 913, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 914, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 915, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 916, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 917, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 918, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 919, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 920, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 921, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 922, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 923, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 924, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 925, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}, {'id': 926, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}, {'id': 951, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 952, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 953, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 954, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 955, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 956, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 957, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 958, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 959, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 960, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 961, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 962, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 963, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}]
2025-06-04 08:33:12,776 - ma_service - DEBUG - rule_numbers值:
[54, 58, 46, 81, 62]
2025-06-04 08:33:12,777 - ma_service - DEBUG - num值:54
2025-06-04 08:33:12,780 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=54
2025-06-04 08:33:12,783 - ma_service - DEBUG - new_dict数据结构:
{'id': 965, 'CPU': 'Intel X86'}
2025-06-04 08:33:12,784 - ma_service - DEBUG - num值:58
2025-06-04 08:33:12,787 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=58
2025-06-04 08:33:12,789 - ma_service - DEBUG - new_dict数据结构:
{'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10'}
2025-06-04 08:33:12,791 - ma_service - DEBUG - num值:46
2025-06-04 08:33:12,794 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=46
2025-06-04 08:33:12,796 - ma_service - DEBUG - new_dict数据结构:
{'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4'}
2025-06-04 08:33:12,797 - ma_service - DEBUG - num值:81
2025-06-04 08:33:12,801 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=81
2025-06-04 08:33:12,803 - ma_service - DEBUG - new_dict数据结构:
{'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的'}
2025-06-04 08:33:12,804 - ma_service - DEBUG - num值:62
2025-06-04 08:33:12,806 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=62
2025-06-04 08:33:12,809 - ma_service - DEBUG - new_dict数据结构:
{'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}
2025-06-04 08:33:12,810 - ma_service - DEBUG - new_list数据结构:
[{'id': 855, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 856, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 857, '用途': '用途 非生产', '操作系统': 'RHEL 8.3', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 858, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 办公', '数据库': 'OceanBase 4'}, {'id': 859, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 860, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 861, '用途': '用途 非生产', '操作系统': 'RHEL 8.10', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 903, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 904, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 905, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 906, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 907, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 908, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 909, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 910, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 911, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 912, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 913, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 914, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 915, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 916, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 917, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 918, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 919, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 920, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 921, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 922, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 923, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 924, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 925, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}, {'id': 926, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}, {'id': 951, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 952, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 953, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 954, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 955, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 956, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 957, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 958, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 959, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 960, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 961, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 962, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 963, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}]
2025-06-04 08:33:12,811 - ma_service - DEBUG - rule_numbers值:
[54, 58, 46, 81, 63]
2025-06-04 08:33:12,812 - ma_service - DEBUG - num值:54
2025-06-04 08:33:12,814 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=54
2025-06-04 08:33:12,817 - ma_service - DEBUG - new_dict数据结构:
{'id': 966, 'CPU': 'Intel X86'}
2025-06-04 08:33:12,819 - ma_service - DEBUG - num值:58
2025-06-04 08:33:12,821 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=58
2025-06-04 08:33:12,823 - ma_service - DEBUG - new_dict数据结构:
{'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10'}
2025-06-04 08:33:12,824 - ma_service - DEBUG - num值:46
2025-06-04 08:33:12,828 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=46
2025-06-04 08:33:12,830 - ma_service - DEBUG - new_dict数据结构:
{'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4'}
2025-06-04 08:33:12,831 - ma_service - DEBUG - num值:81
2025-06-04 08:33:12,833 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=81
2025-06-04 08:33:12,835 - ma_service - DEBUG - new_dict数据结构:
{'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的'}
2025-06-04 08:33:12,836 - ma_service - DEBUG - num值:63
2025-06-04 08:33:12,839 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=63
2025-06-04 08:33:12,841 - ma_service - DEBUG - new_dict数据结构:
{'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}
2025-06-04 08:33:12,842 - ma_service - DEBUG - new_list数据结构:
[{'id': 855, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 856, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 857, '用途': '用途 非生产', '操作系统': 'RHEL 8.3', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 858, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 办公', '数据库': 'OceanBase 4'}, {'id': 859, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 860, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 861, '用途': '用途 非生产', '操作系统': 'RHEL 8.10', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 903, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 904, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 905, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 906, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 907, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 908, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 909, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 910, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 911, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 912, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 913, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 914, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 915, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 916, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 917, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 918, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 919, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 920, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 921, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 922, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 923, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 924, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 925, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}, {'id': 926, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}, {'id': 951, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 952, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 953, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 954, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 955, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 956, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 957, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 958, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 959, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 960, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 961, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 962, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 963, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}]
2025-06-04 08:33:12,843 - ma_service - DEBUG - rule_numbers值:
[54, 58, 46, 82, 60]
2025-06-04 08:33:12,844 - ma_service - DEBUG - num值:54
2025-06-04 08:33:12,847 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=54
2025-06-04 08:33:12,848 - ma_service - DEBUG - new_dict数据结构:
{'id': 967, 'CPU': 'Intel X86'}
2025-06-04 08:33:12,849 - ma_service - DEBUG - num值:58
2025-06-04 08:33:12,852 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=58
2025-06-04 08:33:12,854 - ma_service - DEBUG - new_dict数据结构:
{'id': 967, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10'}
2025-06-04 08:33:12,855 - ma_service - DEBUG - num值:46
2025-06-04 08:33:12,857 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=46
2025-06-04 08:33:12,858 - ma_service - DEBUG - new_dict数据结构:
{'id': 967, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4'}
2025-06-04 08:33:12,859 - ma_service - DEBUG - num值:82
2025-06-04 08:33:12,863 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=82
2025-06-04 08:33:12,865 - ma_service - DEBUG - new_dict数据结构:
{'id': 967, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad'}
2025-06-04 08:33:12,866 - ma_service - DEBUG - num值:60
2025-06-04 08:33:12,868 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=60
2025-06-04 08:33:12,870 - ma_service - DEBUG - new_dict数据结构:
{'id': 967, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}
2025-06-04 08:33:12,872 - ma_service - DEBUG - new_list数据结构:
[{'id': 855, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 856, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 857, '用途': '用途 非生产', '操作系统': 'RHEL 8.3', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 858, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 办公', '数据库': 'OceanBase 4'}, {'id': 859, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 860, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 861, '用途': '用途 非生产', '操作系统': 'RHEL 8.10', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 903, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 904, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 905, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 906, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 907, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 908, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 909, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 910, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 911, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 912, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 913, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 914, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 915, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 916, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 917, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 918, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 919, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 920, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 921, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 922, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 923, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 924, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 925, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}, {'id': 926, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}, {'id': 951, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 952, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 953, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 954, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 955, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 956, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 957, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 958, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 959, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 960, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 961, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 962, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 963, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 967, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}]
2025-06-04 08:33:12,873 - ma_service - DEBUG - rule_numbers值:
[54, 58, 46, 82, 61]
2025-06-04 08:33:12,873 - ma_service - DEBUG - num值:54
2025-06-04 08:33:12,876 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=54
2025-06-04 08:33:12,879 - ma_service - DEBUG - new_dict数据结构:
{'id': 968, 'CPU': 'Intel X86'}
2025-06-04 08:33:12,881 - ma_service - DEBUG - num值:58
2025-06-04 08:33:12,884 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=58
2025-06-04 08:33:12,886 - ma_service - DEBUG - new_dict数据结构:
{'id': 968, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10'}
2025-06-04 08:33:12,887 - ma_service - DEBUG - num值:46
2025-06-04 08:33:12,889 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=46
2025-06-04 08:33:12,893 - ma_service - DEBUG - new_dict数据结构:
{'id': 968, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4'}
2025-06-04 08:33:12,894 - ma_service - DEBUG - num值:82
2025-06-04 08:33:12,898 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=82
2025-06-04 08:33:12,901 - ma_service - DEBUG - new_dict数据结构:
{'id': 968, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad'}
2025-06-04 08:33:12,902 - ma_service - DEBUG - num值:61
2025-06-04 08:33:12,905 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=61
2025-06-04 08:33:12,908 - ma_service - DEBUG - new_dict数据结构:
{'id': 968, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}
2025-06-04 08:33:12,909 - ma_service - DEBUG - new_list数据结构:
[{'id': 855, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 856, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 857, '用途': '用途 非生产', '操作系统': 'RHEL 8.3', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 858, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 办公', '数据库': 'OceanBase 4'}, {'id': 859, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 860, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 861, '用途': '用途 非生产', '操作系统': 'RHEL 8.10', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 903, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 904, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 905, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 906, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 907, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 908, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 909, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 910, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 911, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 912, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 913, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 914, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 915, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 916, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 917, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 918, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 919, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 920, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 921, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 922, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 923, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 924, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 925, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}, {'id': 926, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}, {'id': 951, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 952, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 953, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 954, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 955, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 956, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 957, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 958, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 959, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 960, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 961, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 962, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 963, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 967, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 968, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}]
2025-06-04 08:33:12,910 - ma_service - DEBUG - rule_numbers值:
[54, 58, 46, 82, 62]
2025-06-04 08:33:12,910 - ma_service - DEBUG - num值:54
2025-06-04 08:33:12,913 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=54
2025-06-04 08:33:12,915 - ma_service - DEBUG - new_dict数据结构:
{'id': 969, 'CPU': 'Intel X86'}
2025-06-04 08:33:12,917 - ma_service - DEBUG - num值:58
2025-06-04 08:33:12,920 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=58
2025-06-04 08:33:12,922 - ma_service - DEBUG - new_dict数据结构:
{'id': 969, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10'}
2025-06-04 08:33:12,924 - ma_service - DEBUG - num值:46
2025-06-04 08:33:12,926 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=46
2025-06-04 08:33:12,928 - ma_service - DEBUG - new_dict数据结构:
{'id': 969, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4'}
2025-06-04 08:33:12,929 - ma_service - DEBUG - num值:82
2025-06-04 08:33:12,932 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=82
2025-06-04 08:33:12,934 - ma_service - DEBUG - new_dict数据结构:
{'id': 969, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad'}
2025-06-04 08:33:12,935 - ma_service - DEBUG - num值:62
2025-06-04 08:33:12,939 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=62
2025-06-04 08:33:12,941 - ma_service - DEBUG - new_dict数据结构:
{'id': 969, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}
2025-06-04 08:33:12,943 - ma_service - DEBUG - new_list数据结构:
[{'id': 855, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 856, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 857, '用途': '用途 非生产', '操作系统': 'RHEL 8.3', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 858, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 办公', '数据库': 'OceanBase 4'}, {'id': 859, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 860, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 861, '用途': '用途 非生产', '操作系统': 'RHEL 8.10', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 903, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 904, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 905, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 906, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 907, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 908, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 909, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 910, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 911, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 912, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 913, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 914, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 915, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 916, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 917, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 918, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 919, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 920, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 921, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 922, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 923, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 924, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 925, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}, {'id': 926, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}, {'id': 951, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 952, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 953, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 954, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 955, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 956, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 957, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 958, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 959, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 960, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 961, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 962, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 963, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 967, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 968, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 969, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}]
2025-06-04 08:33:12,944 - ma_service - DEBUG - rule_numbers值:
[54, 58, 46, 82, 63]
2025-06-04 08:33:12,945 - ma_service - DEBUG - num值:54
2025-06-04 08:33:12,948 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=54
2025-06-04 08:33:12,951 - ma_service - DEBUG - new_dict数据结构:
{'id': 970, 'CPU': 'Intel X86'}
2025-06-04 08:33:12,952 - ma_service - DEBUG - num值:58
2025-06-04 08:33:12,955 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=58
2025-06-04 08:33:12,957 - ma_service - DEBUG - new_dict数据结构:
{'id': 970, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10'}
2025-06-04 08:33:12,960 - ma_service - DEBUG - num值:46
2025-06-04 08:33:12,964 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=46
2025-06-04 08:33:12,965 - ma_service - DEBUG - new_dict数据结构:
{'id': 970, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4'}
2025-06-04 08:33:12,967 - ma_service - DEBUG - num值:82
2025-06-04 08:33:12,970 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=82
2025-06-04 08:33:12,972 - ma_service - DEBUG - new_dict数据结构:
{'id': 970, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad'}
2025-06-04 08:33:12,973 - ma_service - DEBUG - num值:63
2025-06-04 08:33:12,976 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=63
2025-06-04 08:33:12,980 - ma_service - DEBUG - new_dict数据结构:
{'id': 970, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}
2025-06-04 08:33:12,981 - ma_service - DEBUG - new_list数据结构:
[{'id': 855, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 856, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 857, '用途': '用途 非生产', '操作系统': 'RHEL 8.3', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 858, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 办公', '数据库': 'OceanBase 4'}, {'id': 859, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 860, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 861, '用途': '用途 非生产', '操作系统': 'RHEL 8.10', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 903, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 904, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 905, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 906, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 907, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 908, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 909, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 910, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 911, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 912, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 913, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 914, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 915, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 916, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 917, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 918, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 919, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 920, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 921, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 922, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 923, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 924, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 925, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}, {'id': 926, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}, {'id': 951, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 952, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 953, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 954, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 955, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 956, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 957, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 958, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 959, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 960, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 961, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 962, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 963, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 967, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 968, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 969, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 970, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}]
2025-06-04 08:33:12,982 - ma_service - DEBUG - rule_numbers值:
[54, 58, 46, 83, 60]
2025-06-04 08:33:12,983 - ma_service - DEBUG - num值:54
2025-06-04 08:33:12,986 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=54
2025-06-04 08:33:12,988 - ma_service - DEBUG - new_dict数据结构:
{'id': 971, 'CPU': 'Intel X86'}
2025-06-04 08:33:12,989 - ma_service - DEBUG - num值:58
2025-06-04 08:33:12,991 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=58
2025-06-04 08:33:12,992 - ma_service - DEBUG - new_dict数据结构:
{'id': 971, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10'}
2025-06-04 08:33:12,993 - ma_service - DEBUG - num值:46
2025-06-04 08:33:12,997 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=46
2025-06-04 08:33:12,999 - ma_service - DEBUG - new_dict数据结构:
{'id': 971, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4'}
2025-06-04 08:33:13,000 - ma_service - DEBUG - num值:83
2025-06-04 08:33:13,002 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=83
2025-06-04 08:33:13,004 - ma_service - DEBUG - new_dict数据结构:
{'id': 971, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj'}
2025-06-04 08:33:13,005 - ma_service - DEBUG - num值:60
2025-06-04 08:33:13,008 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=60
2025-06-04 08:33:13,009 - ma_service - DEBUG - new_dict数据结构:
{'id': 971, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}
2025-06-04 08:33:13,010 - ma_service - DEBUG - new_list数据结构:
[{'id': 855, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 856, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 857, '用途': '用途 非生产', '操作系统': 'RHEL 8.3', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 858, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 办公', '数据库': 'OceanBase 4'}, {'id': 859, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 860, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 861, '用途': '用途 非生产', '操作系统': 'RHEL 8.10', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 903, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 904, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 905, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 906, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 907, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 908, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 909, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 910, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 911, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 912, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 913, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 914, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 915, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 916, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 917, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 918, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 919, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 920, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 921, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 922, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 923, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 924, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 925, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}, {'id': 926, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}, {'id': 951, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 952, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 953, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 954, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 955, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 956, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 957, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 958, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 959, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 960, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 961, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 962, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 963, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 967, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 968, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 969, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 970, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 971, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}]
2025-06-04 08:33:13,012 - ma_service - DEBUG - rule_numbers值:
[54, 58, 46, 83, 61]
2025-06-04 08:33:13,013 - ma_service - DEBUG - num值:54
2025-06-04 08:33:13,016 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=54
2025-06-04 08:33:13,018 - ma_service - DEBUG - new_dict数据结构:
{'id': 972, 'CPU': 'Intel X86'}
2025-06-04 08:33:13,019 - ma_service - DEBUG - num值:58
2025-06-04 08:33:13,022 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=58
2025-06-04 08:33:13,023 - ma_service - DEBUG - new_dict数据结构:
{'id': 972, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10'}
2025-06-04 08:33:13,024 - ma_service - DEBUG - num值:46
2025-06-04 08:33:13,027 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=46
2025-06-04 08:33:13,028 - ma_service - DEBUG - new_dict数据结构:
{'id': 972, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4'}
2025-06-04 08:33:13,029 - ma_service - DEBUG - num值:83
2025-06-04 08:33:13,033 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=83
2025-06-04 08:33:13,035 - ma_service - DEBUG - new_dict数据结构:
{'id': 972, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj'}
2025-06-04 08:33:13,036 - ma_service - DEBUG - num值:61
2025-06-04 08:33:13,038 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=61
2025-06-04 08:33:13,040 - ma_service - DEBUG - new_dict数据结构:
{'id': 972, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}
2025-06-04 08:33:13,041 - ma_service - DEBUG - new_list数据结构:
[{'id': 855, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 856, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 857, '用途': '用途 非生产', '操作系统': 'RHEL 8.3', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 858, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 办公', '数据库': 'OceanBase 4'}, {'id': 859, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 860, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 861, '用途': '用途 非生产', '操作系统': 'RHEL 8.10', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 903, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 904, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 905, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 906, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 907, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 908, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 909, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 910, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 911, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 912, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 913, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 914, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 915, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 916, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 917, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 918, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 919, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 920, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 921, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 922, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 923, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 924, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 925, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}, {'id': 926, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}, {'id': 951, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 952, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 953, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 954, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 955, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 956, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 957, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 958, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 959, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 960, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 961, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 962, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 963, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 967, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 968, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 969, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 970, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 971, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 972, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}]
2025-06-04 08:33:13,042 - ma_service - DEBUG - rule_numbers值:
[54, 58, 46, 83, 62]
2025-06-04 08:33:13,043 - ma_service - DEBUG - num值:54
2025-06-04 08:33:13,045 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=54
2025-06-04 08:33:13,046 - ma_service - DEBUG - new_dict数据结构:
{'id': 973, 'CPU': 'Intel X86'}
2025-06-04 08:33:13,048 - ma_service - DEBUG - num值:58
2025-06-04 08:33:13,051 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=58
2025-06-04 08:33:13,053 - ma_service - DEBUG - new_dict数据结构:
{'id': 973, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10'}
2025-06-04 08:33:13,054 - ma_service - DEBUG - num值:46
2025-06-04 08:33:13,056 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=46
2025-06-04 08:33:13,058 - ma_service - DEBUG - new_dict数据结构:
{'id': 973, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4'}
2025-06-04 08:33:13,059 - ma_service - DEBUG - num值:83
2025-06-04 08:33:13,062 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=83
2025-06-04 08:33:13,063 - ma_service - DEBUG - new_dict数据结构:
{'id': 973, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj'}
2025-06-04 08:33:13,064 - ma_service - DEBUG - num值:62
2025-06-04 08:33:13,068 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=62
2025-06-04 08:33:13,069 - ma_service - DEBUG - new_dict数据结构:
{'id': 973, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}
2025-06-04 08:33:13,070 - ma_service - DEBUG - new_list数据结构:
[{'id': 855, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 856, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 857, '用途': '用途 非生产', '操作系统': 'RHEL 8.3', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 858, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 办公', '数据库': 'OceanBase 4'}, {'id': 859, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 860, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 861, '用途': '用途 非生产', '操作系统': 'RHEL 8.10', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 903, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 904, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 905, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 906, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 907, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 908, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 909, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 910, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 911, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 912, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 913, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 914, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 915, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 916, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 917, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 918, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 919, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 920, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 921, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 922, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 923, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 924, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 925, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}, {'id': 926, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}, {'id': 951, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 952, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 953, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 954, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 955, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 956, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 957, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 958, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 959, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 960, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 961, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 962, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 963, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 967, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 968, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 969, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 970, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 971, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 972, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 973, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}]
2025-06-04 08:33:13,071 - ma_service - DEBUG - rule_numbers值:
[54, 58, 46, 83, 63]
2025-06-04 08:33:13,072 - ma_service - DEBUG - num值:54
2025-06-04 08:33:13,074 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=54
2025-06-04 08:33:13,076 - ma_service - DEBUG - new_dict数据结构:
{'id': 974, 'CPU': 'Intel X86'}
2025-06-04 08:33:13,077 - ma_service - DEBUG - num值:58
2025-06-04 08:33:13,080 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=58
2025-06-04 08:33:13,082 - ma_service - DEBUG - new_dict数据结构:
{'id': 974, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10'}
2025-06-04 08:33:13,083 - ma_service - DEBUG - num值:46
2025-06-04 08:33:13,086 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=46
2025-06-04 08:33:13,088 - ma_service - DEBUG - new_dict数据结构:
{'id': 974, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4'}
2025-06-04 08:33:13,089 - ma_service - DEBUG - num值:83
2025-06-04 08:33:13,093 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=83
2025-06-04 08:33:13,095 - ma_service - DEBUG - new_dict数据结构:
{'id': 974, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj'}
2025-06-04 08:33:13,095 - ma_service - DEBUG - num值:63
2025-06-04 08:33:13,098 - ma_service - DEBUG - select * from rms_software_info where (action='正常' and status='使用中') and id=63
2025-06-04 08:33:13,100 - ma_service - DEBUG - new_dict数据结构:
{'id': 974, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}
2025-06-04 08:33:13,102 - ma_service - DEBUG - new_list数据结构:
[{'id': 855, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 856, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 857, '用途': '用途 非生产', '操作系统': 'RHEL 8.3', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 858, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.3', '用途': '用途 办公', '数据库': 'OceanBase 4'}, {'id': 859, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '用途': '用途 生产', '数据库': 'OceanBase 4'}, {'id': 860, 'CPU': '海光 国产X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', '用途': '用途 灾备'}, {'id': 861, '用途': '用途 非生产', '操作系统': 'RHEL 8.10', 'CPU': '海光 国产X86', '数据库': 'OceanBase 4'}, {'id': 903, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 904, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 905, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 906, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 907, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 908, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 909, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 910, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 911, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 912, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 913, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 914, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 915, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 916, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 917, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 918, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 919, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 920, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 921, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 922, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 923, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 924, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 925, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}, {'id': 926, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.3', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}, {'id': 951, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 生产'}, {'id': 952, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 灾备'}, {'id': 953, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 非生产'}, {'id': 954, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test test', '用途': '用途 办公'}, {'id': 955, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 生产'}, {'id': 956, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 灾备'}, {'id': 957, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 非生产'}, {'id': 958, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1', '用途': '用途 办公'}, {'id': 959, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 生产'}, {'id': 960, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 灾备'}, {'id': 961, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 非生产'}, {'id': 962, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 1212', '用途': '用途 办公'}, {'id': 963, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 生产'}, {'id': 964, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 灾备'}, {'id': 965, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 非生产'}, {'id': 966, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test 2121的', '用途': '用途 办公'}, {'id': 967, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 生产'}, {'id': 968, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 灾备'}, {'id': 969, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 非生产'}, {'id': 970, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test dadadad', '用途': '用途 办公'}, {'id': 971, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 生产'}, {'id': 972, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 灾备'}, {'id': 973, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 非生产'}, {'id': 974, 'CPU': 'Intel X86', '操作系统': 'RHEL 8.10', '数据库': 'OceanBase 4', 'test': 'test jjjjj', '用途': '用途 办公'}]
